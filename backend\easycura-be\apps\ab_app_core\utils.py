import base64
import datetime
import decimal
import random
import string
import time
from typing import Any
from uuid import UUID
from django.utils.encoding import force_text, force_bytes
from django.utils.http import urlsafe_base64_decode, urlsafe_base64_encode
from django.utils import timezone
from django.db.models.query import QuerySet
from apps.ab_app_core import logger
from apps.ab_app_core.exceptions import ABInternalServerException


class Number:
    @staticmethod
    def safe_integer(integer_value: Any, default_value: Any = None) -> Any:
        try:
            return int(integer_value)
        except (ValueError, TypeError, Exception):
            pass
        return default_value

    @staticmethod
    def safe_float(float_value: Any, default_value: Any = None) -> Any:
        try:
            return float(float_value)
        except (ValueError, TypeError, Exception):
            pass
        return default_value

    @staticmethod
    def is_float(float_value: str) -> bool:
        return float_value.replace('.', '', 1).isdigit()

    @staticmethod
    def string_to_decimal(float_value: str) -> decimal.Decimal:
        return decimal.Decimal(float_value)


class Boolean:
    @staticmethod
    def test(value: Any, evaluate_numbers=True) -> bool:
        if isinstance(value, str) and value.lower() in ('false', 'true') + (('0', '1') if evaluate_numbers else ()):
            return True
        return False

    @classmethod
    def value(cls, value: Any, evaluate_numbers=True, default_value: Any = None) -> Any:
        if cls.test(value, evaluate_numbers):
            return isinstance(value, str) and value.lower() in ('true',) + (('1',) if evaluate_numbers else ())
        return default_value


class UID:
    @staticmethod
    def encode_uid(value) -> str:
        return urlsafe_base64_encode(force_bytes(value))

    @staticmethod
    def decode_uid(value):
        return force_text(urlsafe_base64_decode(value))


class UUIDUtilities:
    @staticmethod
    def is_valid(uuid_string: str, version: str = 4):
        try:
            uid_obj = UUID(uuid_string, version=version)
        except (AttributeError, ValueError):
            return False
        return str(uid_obj) == uuid_string


class Date:
    @staticmethod
    def unix_timestamp_to_date(unix_timestamp: str) -> datetime.date:
        return datetime.date.fromtimestamp(float(unix_timestamp))

    @staticmethod
    def unix_timestamp_to_datetime(unix_timestamp: str, tz=None) -> datetime.datetime:
        return datetime.datetime.fromtimestamp(float(unix_timestamp), tz)

    @classmethod
    def unix_timestamp_to_string_date(cls, unix_timestamp: str, export_format: str = '%Y-%m-%d') -> str:
        return cls.date_to_string(datetime.date.fromtimestamp(float(unix_timestamp)), export_format)

    @classmethod
    def unix_timestamp_to_string_datetime(cls, unix_timestamp: str, tz=None,
                                          export_format: str = '%Y-%m-%d %H:%M:%S') -> str:
        return cls.datetime_to_string(datetime.datetime.fromtimestamp(float(unix_timestamp), tz), export_format)

    @staticmethod
    def date_to_unix_timestamp(date: datetime.date):
        return time.mktime(date.timetuple())

    @staticmethod
    def string_to_date(string_date: str, import_format: str = '%Y-%m-%d') -> datetime.date:
        return datetime.datetime.strptime(string_date, import_format).date()

    @staticmethod
    def string_to_datetime(string_date: str, import_format: str = '%Y-%m-%d %H:%M:%S') -> datetime.datetime:
        return datetime.datetime.strptime(string_date, import_format)

    @classmethod
    def string_to_string(cls, string_date: str, import_format: str = '%Y-%m-%d %H:%M:%S',
                         export_format: str = '%Y-%m-%d %H:%M:%S'):
        date = cls.string_to_datetime(string_date, import_format)
        return cls.datetime_to_string(date, export_format)

    @classmethod
    def string_without_year_to_date(cls, string_date: str, year: int, import_format: str = '%Y-%m-%d') -> datetime.date:
        string_date += f"/{year}"
        return cls.string_to_date(string_date, import_format)

    @staticmethod
    def date_to_string(date: datetime.date, export_format: str = '%Y-%m-%d') -> str:
        return date.strftime(export_format)

    @staticmethod
    def datetime_to_string(date: datetime.datetime, export_format: str = '%Y-%m-%d %H:%M:%S') -> str:
        return date.strftime(export_format)

    @staticmethod
    def date_to_datetime(date: datetime.date) -> datetime.datetime:
        return datetime.datetime.combine(date, datetime.datetime.min.time())

    @classmethod
    def verify_expiration_date(cls, expire, exception_class, api_error):
        try:
            expire_date_naive = cls.string_to_datetime(expire)
            expire_date = timezone.make_aware(expire_date_naive)
        except ValueError:
            raise exception_class(api_error.name, api_error)
        else:
            if expire_date < timezone.now():
                raise exception_class(api_error.name, api_error)
        return expire_date

    @staticmethod
    def get_week_number(date):
        return date.isocalendar()[1]


class Image:
    @staticmethod
    def from_string_to_image(image_string):
        return base64.b64decode(image_string)


class String:
    @staticmethod
    def is_none_or_empty(c_string) -> bool:
        return c_string is None or String.is_empty(c_string)

    @staticmethod
    def is_empty(c_string) -> bool:
        return c_string.strip() != ''

    @staticmethod
    def vsprintf(c_string, list_args) -> str:
        for i in range(0, len(list_args)):
            c_string = c_string.replace(f'%{i + 1}$s', str(list_args[i]))
        return c_string

    @staticmethod
    def generate_random_code(length=8, letters=True, letters_only_upper_case=True, letters_only_lower_case=False,
                             digits=True) -> str:
        int_length = Number.safe_integer(length, default_value=8)
        seq = None

        if not letters and not digits:
            return ''

        if letters:
            if letters_only_upper_case:
                seq = string.ascii_uppercase
            elif letters_only_lower_case:
                seq = string.ascii_lowercase
            else:
                seq = string.ascii_letters

        if digits:
            if seq:
                seq += string.digits
            else:
                seq = string.digits

        return ''.join(random.SystemRandom().choice(seq) for _ in range(int_length))

    @staticmethod
    def strip_slashes(string_to_be_evaluated) -> str:
        """
        Returns a string with backslashes stripped off
        :param string_to_be_evaluated:
        :return: string_to_be_evaluated
        """

        string_to_be_evaluated = string_to_be_evaluated.replace("'", "\'")
        string_to_be_evaluated = string_to_be_evaluated.replace('"', '\"')
        return string_to_be_evaluated


class List:
    @staticmethod
    def safe_pop(list_obj: list, position: int) -> list:
        try:
            return list_obj.pop(position)
        except KeyError:
            return list_obj

    @staticmethod
    def smart_append(list_obj: list, new_list: list) -> list:
        if new_list is not None:
            if isinstance(new_list, list):
                list_obj += new_list
            elif isinstance(new_list, QuerySet):
                list_obj += list(new_list)
            else:
                list_obj.append(new_list)


class Dictionary:
    @classmethod
    def safe_pop(cls, dictionary: dict, key: str) -> dict:
        try:
            return dictionary.pop(key)
        except KeyError:
            return dictionary

    @classmethod
    def get_and_pop(cls, dictionary: dict, key: str, default: Any = None) -> dict:
        value = dictionary.get(key, default)
        cls.safe_pop(dictionary, key)
        return value

    @classmethod
    def full_merge_two_dictionary(cls, d1: dict, d2: dict = None) -> dict:
        if not isinstance(d1, dict) or not isinstance(d2, dict):
            return d1

        d_final = d1.copy()

        for k2 in d2.keys():
            if not d1.get(k2):
                d_final[k2] = d2[k2]
            elif isinstance(d2[k2], dict):
                d_final[k2] = cls.full_merge_two_dictionary(d1[k2], d2[k2])

        return d_final

    @classmethod
    def to_string(cls, data: dict) -> str:
        return ' '.join([f"{k}={v}" for k, v in data.items()]) if isinstance(data, dict) else ""


class Reflection:
    @staticmethod
    def get_clazz(class_path: str) -> Any:
        module_path, class_name = class_path.rsplit(".", 1)
        try:
            module = __import__(module_path, fromlist=[class_name])
        except ImportError as e:
            logger.exception("Module '%s' could not be imported" % (module_path,))
            raise e
        try:
            clazz = getattr(module, class_name)
        except AttributeError as e:
            logger.exception("Module '%s' has no class '%s'" % (module_path, class_name,))
            raise e
        return clazz

    @classmethod
    def get_method_from_clazz(cls, class_path: str, method_name: str) -> callable:
        if not class_path or not method_name:
            raise ABInternalServerException('Class_path "%s" or method_name "%s" not valid' % (class_path, method_name))

        clazz = cls.get_clazz(class_path)
        if not hasattr(clazz, method_name):
            raise ABInternalServerException('Class "%s" does not have the method "%s"' % (clazz, method_name))

        return getattr(clazz, method_name)
