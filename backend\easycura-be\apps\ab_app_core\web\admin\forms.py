import os
from django.core import management
from django.conf import settings
from django.forms import forms, TextInput, Char<PERSON>ield
from django.http import FileResponse
from django.utils.timezone import now
from django.utils.translation import ugettext_lazy as _
from apps.ab_app_core.utils import Date


class ExportModelDataAsJSONForm(forms.Form):
    list_model_to_include = CharField(required=False, label=_('List of included model (separated by space)'), widget=TextInput(attrs={'size': 80}))
    list_model_to_exclude = CharField(required=False, label=_('List of excluded model (separated by space)'), widget=TextInput(attrs={'size': 80}))

    def export_model_data_as_json(self):
        list_model_to_include = self.cleaned_data.get('list_model_to_include')
        if list_model_to_include:
            list_model_to_include = list_model_to_include.split(' ')
        else:
            list_model_to_include = ()

        list_model_to_exclude = self.cleaned_data.get('list_model_to_exclude')
        if list_model_to_exclude:
            list_model_to_exclude = list_model_to_exclude.split(' ')
        else:
            list_model_to_exclude = list()

        export_path = os.path.join(settings.MEDIA_ROOT, 'export_model_data.json')
        with open(export_path, 'w+') as f:
            management.call_command('dumpdata', list_model_to_include, exclude=list_model_to_exclude, verbosity=0, stdout=f)

        file = open(export_path, 'rb')
        return FileResponse(file, as_attachment=True, filename=f'export_model_data_at_{Date.datetime_to_string(now(), export_format="%Y-%m-%d_%H-%M-%S")}.json')
