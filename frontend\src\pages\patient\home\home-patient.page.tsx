import React, {useEffect, useState} from 'react';
import Geolocation from '@react-native-community/geolocation';
import Geocoder from 'react-native-geocoding';
import {RefreshControl, Platform, PermissionsAndroid} from 'react-native';
import useLocation from '../../../components/hooks/useLocation';
import {HomePatientPageStyled} from './home-patient.page.styled.ts';
import {useDispatch, useSelector} from 'react-redux';
import {Pages} from '../../../navigation/constants';
import Welcome from '../../../components/shared/welcome/welcome.tsx';
import NewsCard from '../../../components/base/card/news-card/news-card.tsx';
import {
  homepagePatientAction,
  homepagePatientResetState,
  setPatientCurrentPositionAction,
} from '../../../store/patient/home/<USER>';
import CustomCarousel from '../../../components/base/carousel/carousel.tsx';
import MainContainer from '../../../components/base/main-container/main-container.tsx';
import SearchbarCard from '../../../components/shared/search-card/searchbar-card.tsx';
import {navigateToSelectAddressPage} from '../../shared/select-address/constants';
import {useDialog} from '../../../components/base/dialog/hooks/dialog.hook.ts';
import DialogContent from '../../../components/base/dialog/components/dialog-content.tsx';
import {IconType} from '../../../components/base/icon/icon.tsx';
import {strings} from '../../../translation/strings.ts';
import {it} from '../../../translation/supportedlanguage.ts';
import TopProfessions from './components/top-professions/top-professions.tsx';
import TopProfessionals from './components/top-professionals/top-professionals.tsx';
import {useDevice} from '../../../components/base/device-info-context/hooks/device-info-context.hooks.tsx';
import {
  getItemCarouselHeight,
  MIN_HEIGHT_CAROUSEL_ITEM,
} from '../../../components/base/carousel/constants';
import {getFormattedAddressName} from '../../../utils.tsx';
import AvailableProfessionals from './components/available-professionals/available-professionals.tsx';

const HomePatientPage = (props: any) => {
  const dispatch = useDispatch();
  const {openDialog, closeDialog} = useDialog();
  const {getFontSize} = useDevice();

  const [permission, setPermission] = useState(false);
  const [itemCarouselHeight, setItemCarouselHeight] = useState(
    MIN_HEIGHT_CAROUSEL_ITEM,
  );

  const {
    loading,
    dataFetched,
    news,
    professionalTypes,
    currentPosition,
    topProfessionals,
    professionalsAvailable,
  } = useSelector((state: any) => {
    return state.homepagePatient;
  });

  const {professionalTypesList} = useSelector((state: any) => {
    return state.professionalTypes;
  });

  const {user} = useSelector((state: any) => {
    return state.user;
  });

  const getNamePosition = (permission: boolean, position: any) => {
    if (permission && position) {
      if (position.formattedAddress) {
        return position.formattedAddress;
      }
      
      if (position.city && position.country) {
        if (position.region) {
          return `${position.city}, ${position.region}, ${position.country}`;
        }
        return `${position.city}, ${position.country}`;
      }
      
      if (position.subregion && position.country) {
        return `${position.subregion}, ${position.country}`;
      }
      
      if (position.latitude && position.longitude) {
        return `${position.latitude.toFixed(4)}, ${position.longitude.toFixed(4)}`;
      }
      
      return strings(it.homepagePatientNotFound);
    } else {
      return strings(it.homepagePatientNotFound);
    }
  };

  const checkLocationPermission = async () => {
    try {
      if (Platform.OS === 'ios') {
        // For iOS, we need to handle the authorization callback properly
        Geolocation.requestAuthorization(
          () => {
            // Permission granted
            getCurrentUserPosition();
          },
          (error) => {
            // Permission denied
            console.error('iOS location permission denied:', error);
            setPermission(false);
            openDialog(
              <DialogContent
                topIcon={IconType.Warning}
                title={strings(it.attention)}
                text={strings(it.errorMissedPermissionsPosition)}
                buttons={[{ onPress: () => closeDialog(), label: 'OK' }]}
              />,
            );
          }
        );
        return;
      }

      // For Android, check if permissions are already granted
      const fineLocationGranted = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      );
      const coarseLocationGranted = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
      );

      if (fineLocationGranted || coarseLocationGranted) {
        getCurrentUserPosition();
        return;
      }

      // Request permissions if not already granted
      const fine = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Location Permission',
          message: 'EasyCura needs access to your location to find nearby healthcare professionals.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );

      const coarse = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
        {
          title: 'Location Permission',
          message: 'EasyCura needs access to your location to find nearby healthcare professionals.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );

      const granted =
        fine === PermissionsAndroid.RESULTS.GRANTED ||
        coarse === PermissionsAndroid.RESULTS.GRANTED;

      if (granted) {
        getCurrentUserPosition();
      } else {
        setPermission(false);
        openDialog(
          <DialogContent
            topIcon={IconType.Warning}
            title={strings(it.attention)}
            text={strings(it.errorMissedPermissionsPosition)}
            buttons={[{ onPress: () => closeDialog(), label: 'OK' }]}
          />,
        );
      }
    } catch (e) {
      console.error('Permission request error:', e);
      setPermission(false);
    }
  };

  const getCurrentUserPosition = async () => {
    Geolocation.getCurrentPosition(
      position => {
        const {latitude, longitude} = position.coords;
        setPermission(true);
        getLocation({latitude, longitude});
      },
      error => {
        console.error('Geolocation error:', error);
        setPermission(false);
        if (!currentPosition) {
          openDialog(
            <DialogContent
              topIcon={IconType.Warning}
              title={strings(it.attention)}
              text={strings(it.homepagePatientPositionNotFound)}
              buttons={[{onPress: () => closeDialog()}]}
            />,
          );
        }
      },
      {enableHighAccuracy: true, timeout: 20000, maximumAge: 1000},
    );
  };

  const getLocation = (positionCoords: any) => {
    Geocoder.from(positionCoords.latitude, positionCoords.longitude)
      .then(json => {
        if (!json.results || json.results.length === 0) {
          throw new Error('No geocoding results found');
        }
        
        const addressComponent = json.results[0];
        if (!addressComponent.address_components) {
          throw new Error('Invalid address components');
        }
        
        const formattedAddress = getFormattedAddressName(
          addressComponent,
          false,
        );

        const addressData = {
          street:
            addressComponent.address_components.length > 0
              ? addressComponent.address_components[0].long_name
              : '',
          streetNumber:
            addressComponent.address_components.length > 1
              ? addressComponent.address_components[1].long_name
              : '',
          city:
            addressComponent.address_components.length > 2
              ? addressComponent.address_components[2].long_name
              : '',
          postalCode:
            addressComponent.address_components.length > 6
              ? addressComponent.address_components[6].long_name
              : '',
          region:
            addressComponent.address_components.length > 5
              ? addressComponent.address_components[5].long_name
              : '',
          country:
            addressComponent.address_components.length > 7
              ? addressComponent.address_components[7].long_name
              : '',
          formattedAddress,
          subregion:
            addressComponent.address_components.length > 4
              ? addressComponent.address_components[4].long_name
              : '',
          latitude: positionCoords.latitude,
          longitude: positionCoords.longitude,
        };
        
        dispatch(setPatientCurrentPositionAction(addressData));
      })
      .catch((error: any) => {
        const fallbackAddressData = {
          street: '',
          streetNumber: '',
          city: '',
          postalCode: '',
          region: '',
          country: '',
          formattedAddress: `${positionCoords.latitude.toFixed(4)}, ${positionCoords.longitude.toFixed(4)}`,
          subregion: '',
          latitude: positionCoords.latitude,
          longitude: positionCoords.longitude,
        };
        
        dispatch(setPatientCurrentPositionAction(fallbackAddressData));

        // Log the error for debugging but don't show to user since we have fallback
        console.warn('Geocoding failed, using coordinates as fallback:', error.message);

        return;
      });
  };

  const renderItem = ({item}: any, index: number) => {
    return (
      <HomePatientPageStyled.ContainerItemCarousel
        key={index}
        onLayout={(event: any) =>
          getItemCarouselHeight(
            event,
            itemCarouselHeight,
            setItemCarouselHeight,
          )
        }>
        <NewsCard
          title={item.title}
          image={item.img}
          buttonText={item.button_text}
          buttonLink={item.button_link}
        />
      </HomePatientPageStyled.ContainerItemCarousel>
    );
  };

  useEffect(() => {
    dispatch(homepagePatientAction({isLoggedUser: !!user}));
    
    const initializeLocation = async () => {
      try {
        await checkLocationPermission();
      } catch (error) {
      }
    };
    
    initializeLocation();
  }, []);

  useEffect(() => {
    if (props.route.params?.position) {
      dispatch(setPatientCurrentPositionAction(props.route.params?.position));
    }
  }, [props.route.params]);

  return (
    <MainContainer loading={loading}>
      <HomePatientPageStyled.ContainerLists
        contentContainerStyle={{flexGrow: 1}}
        refreshControl={
          <RefreshControl
            refreshing={false}
            onRefresh={() => {
              dispatch(homepagePatientResetState());
              dispatch(homepagePatientAction({isLoggedUser: !!user}));
              
              const refreshLocation = async () => {
                try {
                  await checkLocationPermission();
                } catch (error) {
                  console.error(error);
                }
              };
              
              refreshLocation();
            }}
          />
        }>
        <HomePatientPageStyled.Container>
          <HomePatientPageStyled.ContainerHeader>
            <Welcome navigation={props.navigation} />
            <HomePatientPageStyled.Break />
            <SearchbarCard
              position={getNamePosition(permission, currentPosition)}
              onPressLocation={() => {
                if (permission && currentPosition) {
                  navigateToSelectAddressPage(
                    props.navigation,
                    Pages.PatientNavigation,
                    'position',
                    'HomePatient',
                  );
                } else {
                  checkLocationPermission();
                }
              }}
              onPressSearch={() => {
                props.navigation.navigate(Pages.PatientNavigation, {
                  params: {},
                  screen: 'Search',
                });
              }}
            />
          </HomePatientPageStyled.ContainerHeader>

          {dataFetched && (
            <HomePatientPageStyled.ContainerContentLists>
              <TopProfessions
                professionalTypes={professionalTypes}
                professionalTypesList={professionalTypesList}
                navigation={props.navigation}
              />
              <TopProfessionals
                topProfessionals={topProfessionals}
                navigation={props.navigation}
              />
              <HomePatientPageStyled.NewsContainer>
                {news.newsList.length > 0 && (
                  <HomePatientPageStyled.ContainerCarousel>
                    <CustomCarousel
                      renderItem={renderItem}
                      arrayData={news.newsList}
                      itemHeight={itemCarouselHeight}
                    />
                  </HomePatientPageStyled.ContainerCarousel>
                )}
                {news.error && (
                  <HomePatientPageStyled.ContainerError
                    getFontSize={getFontSize}>
                    {strings(it.homepagePatientErrorNews)}
                  </HomePatientPageStyled.ContainerError>
                )}
              </HomePatientPageStyled.NewsContainer>
              <AvailableProfessionals
                availableProfessionals={professionalsAvailable}
                navigation={props.navigation}
              />
            </HomePatientPageStyled.ContainerContentLists>
          )}
        </HomePatientPageStyled.Container>
      </HomePatientPageStyled.ContainerLists>
    </MainContainer>
  );
};

export default HomePatientPage;
