name: Production Pipeline

on:
  push:
    branches:
      - main
  workflow_dispatch:

env:
  AWS_REGION: eu-central-1
  ECR_REGISTRY: 221854958497.dkr.ecr.eu-central-1.amazonaws.com
  ECR_REPO: easycura-backend-prd
  ECS_CLUSTER: easycura-ecs-prd
  ECS_SERVICE: easycura-backend-service-prd
  IMAGE_TAG: ${{ github.run_number }}
  ENV_PATH: ./stack/production/production.env
  DOCKERFILE_PATH: ./stack/production/django/Dockerfile

jobs:
  build-push-django:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.EASYCURA_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.EASYCURA_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Build, tag, and push image to Amazon ECR
        working-directory: easycura-be
        run: |
          source $ENV_PATH
          docker build --no-cache -t ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPO }}:${{ env.IMAGE_TAG}} --progress plain -f $DOCKERFILE_PATH .
          docker push ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPO }}:${{ env.IMAGE_TAG}}
          export AWS_PAGER=""
          aws ecs update-service \
            --cluster ${{ env.ECS_CLUSTER }} \
            --service ${{ env.ECS_SERVICE }} \
            --force-new-deployment \
            --region eu-central-1 \
            --no-cli-pager

