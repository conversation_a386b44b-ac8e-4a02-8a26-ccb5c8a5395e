import os
import subprocess
from typing import Union
from django import template

register = template.Library()


@register.simple_tag
def get_app_name() -> Union[None, str]:
    return os.environ.get("APP_NAME")


@register.simple_tag
def get_app_version() -> Union[None, str]:
    try:
        version = subprocess.check_output(["git", "describe", "--exact-match", "--tags"]).decode("utf-8").strip()
    except subprocess.CalledProcessError:
        version = os.environ.get("RELEASE_VERSION")

    try:
        git_commit_sha = subprocess.check_output(["git", "rev-parse", "--short", "HEAD"]).decode("utf-8").strip()
    except subprocess.CalledProcessError:
        git_commit_sha = None

    if version and git_commit_sha:
        return f'v. {version} ({git_commit_sha})'
    elif version:
        return f'v. {version}'
    elif git_commit_sha:
        return f'{git_commit_sha}'

    return None
