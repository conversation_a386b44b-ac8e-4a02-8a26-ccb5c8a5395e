from apps.ab_app_core.logger import Logger

# Constants

# Log configurations
app_log_name = 'ab_app_core'
ab_libraries_log_name = 'ab_libraries'

# Settings configurations
app_settings_name = 'AB_APP_CORE_SETTINGS'
ab_app_core_default_upload_folder_path = 'uploads/'
ab_app_core_default_append_timestamp_to_uploaded_file = True
ab_app_core_default_client_email_enabled = True

# Main logger of AB App Core
logger = Logger.get(app_log_name, ab_libraries_log_name)
