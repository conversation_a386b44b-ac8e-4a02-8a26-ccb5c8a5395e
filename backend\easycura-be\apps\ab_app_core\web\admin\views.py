from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import FormView
from apps.ab_app_core import logger
from apps.ab_app_core.web.admin.forms import ExportModelDataAsJSONForm
from apps.ab_app_core.web.auth import SuperAdminProtectedViewContextMixin
from django.utils.translation import ugettext_lazy as _


class ExportModelDataAsJSON(FormView, SuperAdminProtectedViewContextMixin):
    template_name = 'ab_app_core/admin/export-model-data-as-json.html'
    form_class = ExportModelDataAsJSONForm
    success_url = reverse_lazy('export-model-data-as-json')

    def form_valid(self, form):
        try:
            messages.add_message(self.request, messages.SUCCESS, _('Export model data as JSON with success'))
            return form.export_model_data_as_json()
        except (BaseException, Exception) as e:
            msg = 'Exception during export model data as JSON'
            logger.exception(msg)
            messages.add_message(self.request, messages.ERROR, f'{msg}, exc: {e}')
            return super(ExportModelDataAsJSON, self).form_valid(form)
