import os
import sys
import socket
import time
from apps.ab_app_core import logger


class StartServerRoutines:
    @classmethod
    def can_start(cls):
        cls._test_env_vars()
        cls._test_db_connection(os.environ.get('DB_SERVICE'), os.environ.get('DB_PORT'))

    @staticmethod
    def _test_env_vars():
        try:
            os.environ['ENV_ENABLED']
        except KeyError:
            logger.error('Please set the environment variables')
            sys.exit(1)

    @staticmethod
    def _test_db_connection(host, port):
        while True:
            try:
                s = socket.create_connection((host, port), 5)
                s.close()
                break
            except Exception as e:
                logger.error("Can't detect the DB server on host: '%s' port: '%s', exception: %s" % (host, port, e))
                time.sleep(3)
