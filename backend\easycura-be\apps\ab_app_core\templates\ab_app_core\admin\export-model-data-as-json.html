{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify admin_list %}

{% block extrastyle %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static "admin/css/changelists.css" %}">
    <link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}">
    <script src="{% url 'admin:jsi18n' %}"></script>
    {{ media.css }}
    {% if not actions_on_top and not actions_on_bottom %}
        <style>
            #changelist table thead th:first-child {
                width: inherit
            }
        </style>
    {% endif %}
{% endblock %}

{% block coltype %}colM{% endblock %}

{% block bodyclass %}{{ block.super }} app-{{ opts.app_label }} model-{{ opts.model_name }} change-form{% endblock %}

{% if not is_popup %}
    {% block breadcrumbs %}
        <div class="breadcrumbs">
            <a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
            &rsaquo; {% trans "Export model data as JSON" %}
        </div>
    {% endblock %}
{% endif %}

{% block content %}
    <div id="content-main">
        <form id="changelist-form" method="POST">
            <div id="changelist" class="results">
                <table id="result_list">
                    {{ form.as_table }}
                </table>
            </div>
            {% csrf_token %}
            <br/>
            <div class="submit-row">
                <input type="submit" value="{% trans "Export JSON" %}" class="default">
            </div>
        </form>
    </div>
{% endblock %}