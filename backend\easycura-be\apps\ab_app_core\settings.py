from enum import unique, Enum
from typing import Any
from apps.ab_app_core import app_settings_name, ab_app_core_default_upload_folder_path, ab_app_core_default_append_timestamp_to_uploaded_file, \
    ab_app_core_default_client_email_enabled
from apps.ab_app_core.exceptions import ABInternalServerException
from django.conf import settings as django_settings


class ABSettingKey:
    def __init__(self, key_path, default_value=None):
        self.key_path = key_path
        self.default_value = default_value


class ABBaseSettings:
    @staticmethod
    def _get_settings_name() -> str:
        raise ABInternalServerException('Method "_get_settings_name" must be implemented')

    @classmethod
    def _validate_preference(cls, preference: Any) -> ABSettingKey:
        key_enum = getattr(cls, 'Key')
        if not isinstance(preference, key_enum):
            raise ABInternalServerException('Key not valid')
        value = getattr(preference, 'value')
        if not isinstance(value, ABSettingKey):
            raise ABInternalServerException('Key value must be an ABSettingKey instance')
        return value

    @classmethod
    def get(cls, *args, default_value: Any = None) -> Any:
        if len(args) == 0:
            raise ABInternalServerException('Almost one settings must be passed')

        settings_name = cls._get_settings_name()
        if not hasattr(django_settings, settings_name):
            setattr(django_settings, settings_name, dict())

        settings = getattr(django_settings, settings_name)
        if not isinstance(settings, dict):
            raise ABInternalServerException('Settings must be a dict')

        return cls._get_preference(settings, args, default_value)

    @classmethod
    def _get_preference(cls, settings: dict, preferences: tuple, default_value: Any = None) -> Any:
        value = None
        _setting = settings
        for preference in preferences:
            if _setting is None:
                return default_value
            value = cls._validate_preference(preference)
            if isinstance(value.key_path, str):
                _setting = _setting.get(value.key_path)
            elif isinstance(value.key_path, tuple) or isinstance(value.key_path, list):
                for preference_value in value.key_path:
                    if _setting is None:
                        return default_value if default_value else value.default_value
                    _setting = _setting.get(preference_value)
            else:
                raise ABInternalServerException('Key_path value must be a str, tuple or list')
        if _setting is not None:
            return _setting
        if default_value is not None:
            return default_value
        if value is not None:
            return value.default_value


class ABAppCoreSettings(ABBaseSettings):
    @unique
    class Key(Enum):
        UPLOAD_ROOT = ABSettingKey(('UPLOAD_ROOT',), ab_app_core_default_upload_folder_path)
        APPEND_TIMESTAMP_TO_UPLOADED_FILE = ABSettingKey(('APPEND_TIMESTAMP_TO_UPLOADED_FILE',), ab_app_core_default_append_timestamp_to_uploaded_file)
        CLIENT_EMAIL_ENABLED = ABSettingKey(('CLIENT_EMAIL_ENABLED',), ab_app_core_default_client_email_enabled)

    @staticmethod
    def _get_settings_name():
        return app_settings_name


r"""
Available settings:

####################################################
# ABAppCore library
####################################################
AB_APP_CORE_SETTINGS = {
    'UPLOAD_ROOT': 'uploads/',  # Define folder for uploaded file
    'APPEND_TIMESTAMP_TO_UPLOADED_FILE': True,
    'CLIENT_EMAIL_ENABLED': True
}

"""
