from enum import Enum
from typing import Any
from apps.ab_app_core.exceptions import ABException


class ABEnum(Enum):
    def get_by_value(self, value: Any, exception_class: ABException = None, raise_exception: bool = True):
        if not self.has_value(value):
            if isinstance(exception_class, ABException) and raise_exception and callable(exception_class):
                raise exception_class(f'Value {str(value)} does not exist')
            else:
                return None
        if callable(self):
            return self(value)
        return None

    def has_value(self, value: Any):
        return value in self._value2member_map_
