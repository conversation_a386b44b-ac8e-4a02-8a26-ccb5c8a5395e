import os
from typing import Any
from django.core.mail import EmailMessage
from apps.ab_app_core import logger
from apps.ab_app_core.settings import ABAppCoreSettings


class ABEmailAttachment(object):
    def __init__(self, filename: str, content: Any, mimetype: str):
        self.filename = filename
        self.content = content
        self.mimetype = mimetype


class ABClientEmail(object):
    def __init__(self, subject: str, body: str, to: list, from_email: str = None, reply_to: list = None, attachments: [ABEmailAttachment] = None,
                 raise_exception=True):
        self.subject = subject
        self.body = body
        self.to = to
        self.from_email = from_email
        self.reply_to = reply_to
        self.attachments = attachments
        self.raise_exception = raise_exception
        self._set_subject_name_by_env()

    def _set_subject_name_by_env(self):
        env = os.environ.get('DJANGO_ENVIRONMENT')
        prefix = '' if env == 'production' else '[{}] - '.format(env.upper())
        self.subject = f'{prefix}{self.subject}'

    def send(self):
        if not ABAppCoreSettings.get(ABAppCoreSettings.Key.CLIENT_EMAIL_ENABLED):
            logger.info('ABClientEmail disabled')
            return

        try:
            email = EmailMessage(self.subject, self.body, self.from_email, self.to, reply_to=self.reply_to)
            email.content_subtype = 'html'
            if isinstance(self.attachments, list):
                for attachment in self.attachments:
                    email.attach(attachment.filename, attachment.content, attachment.mimetype)
            email.send(not self.raise_exception)
        except (BaseException, Exception) as e:
            self._handle_exception(e)

    def _handle_exception(self, exc: BaseException):
        logger.exception('ABClientEmail exception: ')
        if self.raise_exception:
            raise exc
