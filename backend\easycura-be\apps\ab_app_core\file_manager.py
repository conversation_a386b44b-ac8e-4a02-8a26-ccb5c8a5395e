import hashlib
import mimetypes
import re
import uuid
import os
import time
from typing import Any
import humanfriendly
from apps.ab_app_core.settings import ABAppCoreSettings


class SimpleFileManager:
    @classmethod
    def _add_timestamp_to_file(cls, filename: str) -> str:
        filename, extension = os.path.splitext(filename)
        filename += f'____{time.time()}{extension}'
        return filename

    @classmethod
    def _remove_timestamp_to_file(cls, filename: str) -> str:
        filename = re.sub(r'____[0-9]*\.[0-9]*', '', filename)
        return filename

    @staticmethod
    def calculate_hash_file(file_path: str):
        hash_calc = hashlib.md5()
        with open(file_path, 'rb') as f:
            buf = f.read()
            hash_calc.update(buf)
        return hash_calc.hexdigest()

    @classmethod
    def define_file_path_upload(cls, instance: Any, filename: str) -> str:
        if ABAppCoreSettings.get(ABAppCoreSettings.Key.APPEND_TIMESTAMP_TO_UPLOADED_FILE):
            filename = cls._add_timestamp_to_file(filename)
        upload_folder = ABAppCoreSettings.get(ABAppCoreSettings.Key.UPLOAD_ROOT)
        path = os.path.join(upload_folder)
        if not os.path.exists(os.path.dirname(path)):
            os.makedirs(os.path.dirname(path))
        path = os.path.join(path, filename)
        return path

    @staticmethod
    def create_symbolic_link_file_in_directory(source_path: str, sym_path: str, prefix_title: str = None) -> str:
        filename, extension = os.path.splitext(os.path.basename(os.path.normpath(source_path)))

        unique_title = str(uuid.uuid4()) + extension
        if isinstance(prefix_title, str):
            unique_title = '{}_{}'.format(re.sub(r"\W", "_", prefix_title), unique_title)
        sym_path = os.path.join(sym_path, unique_title)

        if not os.path.exists(os.path.dirname(sym_path)):
            os.makedirs(os.path.dirname(sym_path))

        os.symlink(source_path, sym_path)

        return sym_path

    @staticmethod
    def get_mime_type_file(file_path: str, force_download: bool = False) -> str:
        if force_download:
            return 'application/force-download'
        try:
            mime_type = mimetypes.MimeTypes().guess_type(file_path)[0]
        except (BaseException, Exception):
            mime_type = 'application/octet-stream'

        return mime_type

    @classmethod
    def get_original_filename(cls, file_path: str) -> str:
        filename = os.path.basename(os.path.normpath(file_path))
        return cls._remove_timestamp_to_file(filename)

    @classmethod
    def get_file_size(cls, file_path: str) -> float:
        return os.path.getsize(file_path)

    @classmethod
    def get_file_size_human_readable(cls, file_path: str) -> str:
        return humanfriendly.format_size(cls.get_file_size(file_path))
