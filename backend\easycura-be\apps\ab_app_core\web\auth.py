from django.contrib.auth.mixins import UserPassesTestMixin
from django.views import View
from django.views.generic.base import ContextMixin


class AdminProtectedView(UserPassesTestMixin, View):
    def test_func(self):
        return self.request.user.is_staff or self.request.user.is_superuser


class SuperAdminProtectedView(UserPassesTestMixin, View):
    def test_func(self):
        return self.request.user.is_superuser


class AdminProtectedViewContextMixin(ContextMixin, AdminProtectedView):
    def get_context_data(self, **kwargs):
        context = super(AdminProtectedViewContextMixin, self).get_context_data(**kwargs)
        context['site_url'] = '/'
        context['has_permission'] = self.test_func()
        return context


class SuperAdminProtectedViewContextMixin(ContextMixin, SuperAdminProtectedView):
    def get_context_data(self, **kwargs):
        context = super(SuperAdminProtectedViewContextMixin, self).get_context_data(**kwargs)
        context['site_url'] = '/'
        context['has_permission'] = self.test_func()
        return context
